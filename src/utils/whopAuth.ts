import { whopApiClient, isWhopContext } from '@/lib/whop-sdk';

export interface WhopUser {
  id: string;
  username: string;
  email?: string;
  profilePicUrl?: string;
  isWhopUser: true;
}

export interface WhopAccessResult {
  hasAccess: boolean;
  accessLevel: 'admin' | 'customer' | 'no_access';
  userId?: string;
  experienceId?: string;
}

/**
 * Get current Whop user (server extracts token from headers)
 */
export const getCurrentWhopUser = async (): Promise<WhopUser | null> => {
  try {
    console.log('🔐 Getting current Whop user...');

    // Call server-side API to get current user (server extracts token from headers)
    const response = await whopApiClient.getCurrentUser();

    if (!response.success || !response.user) {
      console.warn('❌ Failed to get current Whop user');
      return null;
    }

    const user = response.user;
    console.log('✅ Whop user retrieved:', { userId: user.id, username: user.username });

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      profilePicUrl: user.profilePicUrl,
      isWhopUser: true
    };
  } catch (error) {
    // Console logging removed
    return null;
  }
};

/**
 * Legacy function for backward compatibility
 */
export const verifyWhopUserToken = async (_token?: string): Promise<WhopUser | null> => {
  console.warn('⚠️ verifyWhopUserToken is deprecated, use getCurrentWhopUser() instead');
  return getCurrentWhopUser();
};

/**
 * Check if user has access to a specific experience
 */
export const checkWhopAccess = async (
  userId: string,
  experienceId: string
): Promise<WhopAccessResult> => {
  try {
    console.log('🔍 Checking Whop access for user:', { userId, experienceId });

    // Call server-side API to check access
    const response = await whopApiClient.checkAccess(userId, experienceId);

    console.log('📋 Whop API response:', {
      success: response.success,
      hasAccess: response.access?.hasAccess,
      accessLevel: response.access?.accessLevel,
      error: response.error
    });

    if (!response.success || !response.access) {
      console.warn('❌ Failed to check Whop access:', {
        responseSuccess: response.success,
        hasAccessData: !!response.access,
        error: response.error
      });
      return {
        hasAccess: false,
        accessLevel: 'no_access',
        userId,
        experienceId
      };
    }

    const result = response.access;
    console.log('✅ Whop access check result:', {
      hasAccess: result.hasAccess,
      accessLevel: result.accessLevel,
      userId,
      experienceId
    });

    return {
      hasAccess: result.hasAccess,
      accessLevel: result.accessLevel,
      userId,
      experienceId
    };
  } catch (error) {
    console.error('❌ Error checking Whop access:', {
      error: error.message,
      userId,
      experienceId
    });
    return {
      hasAccess: false,
      accessLevel: 'no_access',
      userId,
      experienceId
    };
  }
};

/**
 * Check if we're in a Whop iframe context
 * Note: We don't extract tokens client-side anymore - server handles that
 * Enhanced for iOS compatibility
 */
export const isInWhopIframeContext = (): boolean => {
  if (typeof window === 'undefined') {
    return false;
  }

  // Check if we're in an iframe
  const isInIframe = window !== window.parent;

  // Check for Whop-specific indicators
  let isWhopContext = false;
  let parentHostname = 'unknown';

  try {
    // Try to access parent hostname (will fail in cross-origin iframe)
    parentHostname = window.parent?.location?.hostname || 'unknown';
    isWhopContext = parentHostname === 'whop.com';
  } catch (error) {
    // Cross-origin error means we're in an iframe from a different domain
    // This is expected for Whop iframes
    console.log('🔒 Cross-origin iframe detected (expected for Whop)');

    // Enhanced iOS detection - iOS Safari has stricter iframe policies
    const userAgent = navigator.userAgent || '';
    const isIOS = /iPad|iPhone|iPod/.test(userAgent);
    const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);

    // Check other indicators with iOS-specific fallbacks
    isWhopContext = document.referrer.includes('whop.com') ||
                   window.location.pathname.startsWith('/experiences/') ||
                   new URLSearchParams(window.location.search).has('whop') ||
                   new URLSearchParams(window.location.search).has('experienceId') ||
                   new URLSearchParams(window.location.search).has('experience') ||
                   localStorage.getItem('whop_experience_id') !== null ||
                   (window.location.hostname === 'localhost' && window.location.port === '3001') || // Whop proxy
                   // iOS-specific: Check for Whop app context even without clear iframe indicators
                   (isIOS && (
                     document.referrer.includes('whop') ||
                     window.location.search.includes('whop') ||
                     window.location.hash.includes('whop') ||
                     localStorage.getItem('whop_auth_state') === 'true'
                   ));
  }

  // For iOS, be more lenient with iframe detection since iOS Safari can behave differently
  const userAgent = navigator.userAgent || '';
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);

  // On iOS, if we have Whop context indicators, consider it valid even if iframe detection is unclear
  const result = (isInIframe && isWhopContext) || (isIOS && isWhopContext);

  console.log('🔍 Whop iframe context check:', {
    isInIframe,
    isWhopContext,
    parentHostname,
    referrer: document.referrer,
    hasExperiencePath: window.location.pathname.startsWith('/experiences/'),
    hasWhopParam: new URLSearchParams(window.location.search).has('whop'),
    isIOS,
    userAgent: userAgent.substring(0, 50) + '...',
    storedWhopState: localStorage.getItem('whop_auth_state'),
    result
  });

  return result;
};

/**
 * Legacy function - tokens are now handled server-side
 */
export const getWhopUserTokenFromContext = (): string | null => {
  return null;
};

/**
 * Store Whop user token for development purposes
 */
export const storeWhopUserToken = (token: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('whop_user_token', token);
  }
};

/**
 * Clear stored Whop user token
 */
export const clearWhopUserToken = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('whop_user_token');
  }
};

/**
 * Check if current context is a Whop environment
 * Enhanced for iOS compatibility
 */
export const isInWhopEnvironment = (): boolean => {
  if (typeof window === 'undefined') return false;

  // Use the proper iframe detection
  const isInWhopIframe = isInWhopIframeContext();

  // Check URL-based indicators
  const urlIndicators = isWhopContext();

  // Additional iOS-specific checks
  const userAgent = navigator.userAgent || '';
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);

  // Check for stored Whop state (important for iOS where context might be lost)
  const hasStoredWhopState = localStorage.getItem('whop_auth_state') === 'true' ||
                             localStorage.getItem('whop_experience_id') !== null ||
                             localStorage.getItem('whop_user_data') !== null;

  // Check for experience ID in various locations
  const hasExperienceId = getExperienceIdFromUrl() !== null;

  // On iOS, be more permissive - if we have any Whop indicators, consider it a Whop environment
  const iosWhopIndicators = isIOS && (
    hasStoredWhopState ||
    hasExperienceId ||
    document.referrer.includes('whop') ||
    window.location.search.includes('experience') ||
    window.location.hash.includes('experience')
  );

  const result = isInWhopIframe || urlIndicators || iosWhopIndicators;

  console.log('🔍 Whop environment check:', {
    isInWhopIframe,
    urlIndicators,
    isIOS,
    hasStoredWhopState,
    hasExperienceId,
    iosWhopIndicators,
    result
  });

  return result;
};

/**
 * Get experience ID from current URL
 * Enhanced for standalone trade-app deployment and iOS compatibility
 */
export const getExperienceIdFromUrl = (): string | null => {
  if (typeof window === 'undefined') {
    return null;
  }

  const userAgent = navigator.userAgent || '';
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);

  // Method 1: Check for experience URL path (original Whop iframe context)
  const pathMatch = window.location.pathname.match(/\/experiences\/([^\/]+)/);
  if (pathMatch) {
    // Store it for iOS persistence
    if (isIOS) {
      try {
        localStorage.setItem('whop_experience_id', pathMatch[1]);
      } catch (error) {
        // Failed to store experience ID on iOS
      }
    }
    return pathMatch[1];
  }

  // Method 2: Check for experience ID in URL parameters (standalone deployment)
  const urlParams = new URLSearchParams(window.location.search);
  const experienceParam = urlParams.get('experience') || urlParams.get('experienceId') || urlParams.get('exp');
  if (experienceParam) {
    console.log('🔍 Found experience ID in URL parameters:', experienceParam);
    // Store it for iOS persistence
    if (isIOS) {
      try {
        localStorage.setItem('whop_experience_id', experienceParam);
      } catch (error) {
        console.warn('⚠️ Failed to store experience ID on iOS:', error);
      }
    }
    return experienceParam;
  }

  // Method 3: Check for experience ID in URL hash (fallback)
  const hashParams = new URLSearchParams(window.location.hash.substring(1));
  const hashExperience = hashParams.get('experience') || hashParams.get('experienceId') || hashParams.get('exp');
  if (hashExperience) {
    console.log('🔍 Found experience ID in URL hash:', hashExperience);
    // Store it for iOS persistence
    if (isIOS) {
      try {
        localStorage.setItem('whop_experience_id', hashExperience);
      } catch (error) {
        console.warn('⚠️ Failed to store experience ID on iOS:', error);
      }
    }
    return hashExperience;
  }

  // Method 4: Check localStorage for previously stored experience ID (important for iOS)
  try {
    const storedExperienceId = localStorage.getItem('whop_experience_id');
    if (storedExperienceId) {
      console.log('🔍 Found experience ID in localStorage:', storedExperienceId);
      return storedExperienceId;
    }
  } catch (error) {
    console.warn('⚠️ Failed to read from localStorage:', error);
  }

  // Method 5: iOS-specific fallback - check for known trading experience IDs
  if (isIOS) {
    // Check if we're on a trading-related path and have stored Whop state
    const isOnTradingPath = window.location.pathname.includes('/trade') ||
                           window.location.pathname.includes('/trading');
    const hasWhopState = localStorage.getItem('whop_auth_state') === 'true';

    if (isOnTradingPath && hasWhopState) {
      // Use the known trading experience ID as fallback
      const fallbackExperienceId = 'exp_ThljdpAF70d4Af'; // Known trading experience ID
      console.log('🔍 iOS fallback: Using known trading experience ID:', fallbackExperienceId);
      try {
        localStorage.setItem('whop_experience_id', fallbackExperienceId);
      } catch (error) {
        console.warn('⚠️ Failed to store fallback experience ID on iOS:', error);
      }
      return fallbackExperienceId;
    }
  }

  console.log('⚠️ No experience ID found in URL or storage');
  return null;
};

/**
 * Initialize Whop authentication context
 */
export const initializeWhopAuth = async (): Promise<{
  isWhopUser: boolean;
  user: WhopUser | null;
  accessResult: WhopAccessResult | null;
}> => {
  console.log('🚀 Initializing Whop authentication...');

  // Check if we're in a Whop iframe context
  if (!isInWhopIframeContext() && !isInWhopEnvironment()) {
    console.log('ℹ️ Not in Whop environment, skipping Whop auth');
    return {
      isWhopUser: false,
      user: null,
      accessResult: null
    };
  }

  // Get current user from server (server extracts token from headers)
  const user = await getCurrentWhopUser();
  if (!user) {
    console.log('ℹ️ No Whop user found');
    return {
      isWhopUser: false,
      user: null,
      accessResult: null
    };
  }

  // Check access if we have an experience ID
  const experienceId = getExperienceIdFromUrl();
  let accessResult: WhopAccessResult | null = null;

  console.log('🔍 Experience ID from URL:', experienceId);

  if (experienceId) {
    console.log('🔍 Checking access for experience:', experienceId);
    accessResult = await checkWhopAccess(user.id, experienceId);
  } else {
    console.log('⚠️ No experience ID found, skipping access check');
  }

  console.log('✅ Whop authentication initialized:', {
    user: user.username,
    userId: user.id,
    experienceId,
    companyId: import.meta.env.VITE_WHOP_COMPANY_ID,
    hasAccess: accessResult?.hasAccess,
    accessLevel: accessResult?.accessLevel,
    accessResult
  });

  return {
    isWhopUser: true,
    user,
    accessResult
  };
};
