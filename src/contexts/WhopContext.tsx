import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { whopIntermediaryClient, WhopUser, WhopAccessResult } from '@/lib/whopIntermediaryClient';
import { getExperienceIdFromUrl } from '@/utils/whopAuth';

import { supabase } from '@/integrations/supabase/client';

// localStorage keys for persisting Whop auth state
const WHOP_AUTH_STORAGE_KEY = 'whop_auth_state';
const WHOP_USER_STORAGE_KEY = 'whop_user_data';
const WHOP_ACCESS_STORAGE_KEY = 'whop_access_result';
const WHOP_EXPERIENCE_ID_KEY = 'whop_experience_id';

// Utility functions for localStorage persistence
const saveWhopAuthToStorage = (isWhopUser: boolean, user: WhopUser | null, accessResult: WhopAccessResult | null, experienceId: string | null) => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(WHOP_AUTH_STORAGE_KEY, JSON.stringify(isWhopUser));
    if (user) {
      localStorage.setItem(WHOP_USER_STORAGE_KEY, JSON.stringify(user));
    } else {
      localStorage.removeItem(WHOP_USER_STORAGE_KEY);
    }
    if (accessResult) {
      localStorage.setItem(WHOP_ACCESS_STORAGE_KEY, JSON.stringify(accessResult));
    } else {
      localStorage.removeItem(WHOP_ACCESS_STORAGE_KEY);
    }
  } catch (error) {
    console.error('❌ Failed to save Whop auth to localStorage:', error);
  }
};

const loadWhopAuthFromStorage = (): { isWhopUser: boolean; user: WhopUser | null; accessResult: WhopAccessResult | null } => {
  if (typeof window === 'undefined') {
    return { isWhopUser: false, user: null, accessResult: null };
  }

  try {
    const isWhopUser = JSON.parse(localStorage.getItem(WHOP_AUTH_STORAGE_KEY) || 'false');
    const user = localStorage.getItem(WHOP_USER_STORAGE_KEY)
      ? JSON.parse(localStorage.getItem(WHOP_USER_STORAGE_KEY)!)
      : null;
    const accessResult = localStorage.getItem(WHOP_ACCESS_STORAGE_KEY)
      ? JSON.parse(localStorage.getItem(WHOP_ACCESS_STORAGE_KEY)!)
      : null;

    return { isWhopUser, user, accessResult };
  } catch (error) {
    console.error('❌ Failed to load Whop auth from localStorage:', error);
    return { isWhopUser: false, user: null, accessResult: null };
  }
};

const clearWhopAuthFromStorage = () => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.removeItem(WHOP_AUTH_STORAGE_KEY);
    localStorage.removeItem(WHOP_USER_STORAGE_KEY);
    localStorage.removeItem(WHOP_ACCESS_STORAGE_KEY);
    localStorage.removeItem(WHOP_EXPERIENCE_ID_KEY);
  } catch (error) {
    console.error('❌ Failed to clear Whop auth from localStorage:', error);
  }
};

// Utility functions for experience ID persistence
const saveExperienceIdToStorage = (experienceId: string | null) => {
  if (typeof window === 'undefined') return;

  try {
    if (experienceId) {
      localStorage.setItem(WHOP_EXPERIENCE_ID_KEY, experienceId);
    } else {
      localStorage.removeItem(WHOP_EXPERIENCE_ID_KEY);
    }
  } catch (error) {
    console.error('❌ Failed to save experience ID to localStorage:', error);
  }
};

const loadExperienceIdFromStorage = (): string | null => {
  if (typeof window === 'undefined') return null;

  try {
    const experienceId = localStorage.getItem(WHOP_EXPERIENCE_ID_KEY);
    return experienceId;
  } catch (error) {
    console.error('❌ Failed to load experience ID from localStorage:', error);
    return null;
  }
};

interface WhopContextType {
  // State
  isWhopUser: boolean;
  whopUser: WhopUser | null;
  accessResult: WhopAccessResult | null;
  experienceId: string | null;
  isLoading: boolean;
  error: string | null;
  hasSupabaseSession: boolean;

  // Actions
  refreshWhopAuth: () => Promise<void>;
  clearWhopAuth: () => Promise<void>;
  setExperienceIdAndSave: (experienceId: string) => void;
}

const WhopContext = createContext<WhopContextType | undefined>(undefined);

interface WhopProviderProps {
  children: ReactNode;
}

export const WhopProvider: React.FC<WhopProviderProps> = ({ children }) => {
  // Initialize state from localStorage if available
  const storedAuth = loadWhopAuthFromStorage();
  const storedExperienceId = loadExperienceIdFromStorage();
  const [isWhopUser, setIsWhopUser] = useState(storedAuth.isWhopUser);
  const [whopUser, setWhopUser] = useState<WhopUser | null>(storedAuth.user);
  const [accessResult, setAccessResult] = useState<WhopAccessResult | null>(storedAuth.accessResult);
  const [experienceId, setExperienceId] = useState<string | null>(storedExperienceId);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasSupabaseSession, setHasSupabaseSession] = useState<boolean>(false);

  const refreshWhopAuth = async (retryCount = 0) => {
    const maxRetries = 2;

    try {
      setIsLoading(true);
      setError(null);

      console.group('🔄 Whop Auth Refresh Debug');
      console.log('📊 Refresh State:', {
        retryCount,
        maxRetries,
        currentIsWhopUser: isWhopUser,
        currentWhopUser: whopUser?.username,
        currentExperienceId: experienceId,
        hasSupabaseSession,
        timestamp: new Date().toISOString()
      });

      // Check for development mode overrides
      // const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

      // Check if we already have an active Supabase session for a Whop user
      const { data: { user: existingUser } } = await supabase.auth.getUser();
      if (existingUser && existingUser.user_metadata?.isWhopUser) {
        // If we have stored Whop user data, use it
        const storedAuth = loadWhopAuthFromStorage();
        if (storedAuth.isWhopUser && storedAuth.user) {
          setIsWhopUser(true);
          setWhopUser(storedAuth.user);
          setAccessResult(storedAuth.accessResult);
          setIsLoading(false);
          return;
        }
      } else {
        // No Supabase session found, but check if we have stored Whop user data
        const storedAuth = loadWhopAuthFromStorage();
        if (storedAuth.isWhopUser && storedAuth.user) {
          const reAuthSuccess = await reAuthenticateWhopUser(storedAuth.user);
          if (reAuthSuccess) {
            setIsWhopUser(true);
            setWhopUser(storedAuth.user);
            setAccessResult(storedAuth.accessResult);
            setIsLoading(false);
            return;
          } else {
            console.error('❌ Critical: Failed to re-authenticate Whop user:', storedAuth.user.username);
            // Clear stored auth data since re-authentication failed
            clearWhopAuthFromStorage();
          }
        }
      }

      // Get current experience ID from URL or use stored one
      const currentExperienceId = getExperienceIdFromUrl();

      if (currentExperienceId) {
        // We're on an experience URL, save it for later use
        setExperienceId(currentExperienceId);
        saveExperienceIdToStorage(currentExperienceId);
      } else if (storedExperienceId) {
        // We're not on an experience URL but have a stored one (e.g., after redirect to /trading)
        setExperienceId(storedExperienceId);
      } else {
        setExperienceId(null);
      }


      // Initialize Whop auth via intermediary server
      const userAgent = navigator.userAgent || '';
      const isIOS = /iPad|iPhone|iPod/.test(userAgent);

      const authResult = await whopIntermediaryClient.initializeAuth();

      // iOS-specific fallback: If auth failed but we have indicators of being in a Whop context
      if (!authResult.isWhopUser && isIOS) {
        const hasWhopIndicators = (
          currentExperienceId ||
          storedExperienceId ||
          window.location.pathname.includes('/trade') ||
          window.location.pathname.includes('/trading') ||
          localStorage.getItem('whop_auth_state') === 'true' ||
          document.referrer.includes('whop')
        );

        if (hasWhopIndicators) {
          // Check if we have stored admin access from previous session
          const storedAuth = loadWhopAuthFromStorage();
          const wasAdmin = storedAuth.accessResult?.accessLevel === 'admin';

          // Create a fallback user object for iOS, preserving admin status if available
          const fallbackUser = {
            id: storedAuth.user?.id || 'ios_fallback_user',
            username: storedAuth.user?.username || 'iOS User',
            email: storedAuth.user?.email || '<EMAIL>'
          };

          const fallbackAccess = {
            hasAccess: true,
            // Preserve admin status if we had it before, otherwise default to customer
            accessLevel: wasAdmin ? 'admin' as const : 'customer' as const,
            userId: fallbackUser.id,
            companyId: storedAuth.accessResult?.companyId || 'fallback',
            timestamp: new Date().toISOString()
          };

          console.log('📱 iOS fallback auth created:', {
            wasAdmin,
            preservedAccessLevel: fallbackAccess.accessLevel,
            storedAuth: !!storedAuth.user
          });

          setIsWhopUser(true);
          setWhopUser(fallbackUser);
          setAccessResult(fallbackAccess);

          // Save fallback state
          saveWhopAuthToStorage(true, fallbackUser, fallbackAccess, currentExperienceId || storedExperienceId);

        } else {
          setIsWhopUser(authResult.isWhopUser);
          setWhopUser(authResult.user);
          setAccessResult(authResult.accessResult);

          // Save access level separately for mobile restoration
          if (authResult.accessResult?.accessLevel) {
            localStorage.setItem('whop_access_level', authResult.accessResult.accessLevel);
            console.log('💾 Saved access level for mobile:', authResult.accessResult.accessLevel);
          }

          // Save to localStorage for persistence across page navigation
          saveWhopAuthToStorage(authResult.isWhopUser, authResult.user, authResult.accessResult, currentExperienceId);
        }
      } else {
        setIsWhopUser(authResult.isWhopUser);
        setWhopUser(authResult.user);
        setAccessResult(authResult.accessResult);

        // Save access level separately for mobile restoration
        if (authResult.accessResult?.accessLevel) {
          localStorage.setItem('whop_access_level', authResult.accessResult.accessLevel);
        }

        // Save to localStorage for persistence across page navigation
        saveWhopAuthToStorage(authResult.isWhopUser, authResult.user, authResult.accessResult, currentExperienceId);
      }

      // Get the final state after potential iOS fallback
      const finalIsWhopUser = !authResult.isWhopUser && isIOS ? true : authResult.isWhopUser;
      const finalWhopUser = !authResult.isWhopUser && isIOS && (currentExperienceId || storedExperienceId || window.location.pathname.includes('/trade')) ?
        { id: 'ios_fallback_user', username: 'iOS User', email: '<EMAIL>' } : authResult.user;

      if (finalIsWhopUser && finalWhopUser) {
        // Create/register Supabase user for Whop user with automatic sign-in
        // Skip for iOS fallback users since they don't have real Whop credentials
        if (finalWhopUser.id !== 'ios_fallback_user') {
          try {
            // Import the whopApiClient here to avoid circular dependencies
            const { whopApiClient } = await import('@/lib/whop-sdk');
            const supabaseResult = await whopApiClient.createSupabaseUser(authResult.accessResult);

          if (supabaseResult.success && supabaseResult.credentials) {
            // Automatically sign in the Whop user with the generated credentials
            try {
              const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
                email: supabaseResult.credentials.email,
                password: supabaseResult.credentials.password
              });

              if (signInError) {
                console.error('❌ Failed to automatically sign in Whop user:', signInError);
                setHasSupabaseSession(false);

                // Still consider this a success since the user was created
                console.warn('⚠️ User created but automatic sign-in failed - user will need to refresh');
              } else if (signInData.session) {
                console.log('✅ Whop user automatically signed in successfully');
                setHasSupabaseSession(true);

                // Verify the session is properly established
                const { data: { user: sessionUser } } = await supabase.auth.getUser();
                if (sessionUser && sessionUser.user_metadata?.isWhopUser) {
                  console.log('✅ Whop user session verified:', sessionUser.id);
                } else {
                  console.warn('⚠️ Session created but user verification failed');
                }
              } else {
                console.warn('⚠️ Sign-in succeeded but no session returned');
                setHasSupabaseSession(false);
              }
            } catch (signInError) {
              console.error('❌ Exception during automatic sign-in:', signInError);
              setHasSupabaseSession(false);
            }
          } else {
            console.error('❌ Failed to create Whop user in Supabase:', supabaseResult.error);
            setHasSupabaseSession(false);
          }
          } catch (supabaseError) {
            console.error('❌ Error creating Whop user in Supabase:', supabaseError);
            setHasSupabaseSession(false);

            // Don't retry for Supabase errors
            console.warn('⚠️ Supabase registration error');
          }
        } else {
          console.log('🍎 iOS fallback user detected, skipping Supabase user creation');
          setHasSupabaseSession(false);
        }
      } else {
        console.log('ℹ️ No Whop user detected');
        setHasSupabaseSession(false);
      }
      
    } catch (err) {
      console.error('❌ Error refreshing Whop auth:', err);

      // Check if we have any Whop user data to determine if this is a critical error
      const hasWhopUserData = isWhopUser && whopUser;
      const isCriticalError = !hasWhopUserData;

      if (isCriticalError && retryCount < maxRetries) {
        console.log(`🔄 Retrying Whop authentication due to critical error in 3 seconds... (${retryCount + 1}/${maxRetries})`);
        setTimeout(() => refreshWhopAuth(retryCount + 1), 3000);
        return;
      }

      // For non-critical errors or after max retries, continue with what we have
      if (!isCriticalError) {
        console.warn('⚠️ Non-critical error in Whop auth, continuing with existing state');
      } else {
        // Final failure after all retries for critical errors
        setError(`Authentication failed after ${maxRetries + 1} attempts: ${err instanceof Error ? err.message : 'Unknown error'}`);
        setIsWhopUser(false);
        setWhopUser(null);
        setAccessResult(null);

        // Clear localStorage on critical error
        clearWhopAuthFromStorage();
      }
    } finally {
      // Always set loading to false - don't keep the user waiting indefinitely
      setIsLoading(false);
      console.groupEnd();
    }
  };

  // Re-authenticate a Whop user who has lost their Supabase session
  const reAuthenticateWhopUser = async (whopUserData: WhopUser): Promise<boolean> => {
    try {
      console.log('🔄 Re-authenticating Whop user:', whopUserData.username);

      // Generate the same credentials that would have been used during initial creation
      const email = `${whopUserData.id}@whop.app`;
      const password = whopUserData.id;

      console.log('🔐 Attempting to sign in with stored credentials...');
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: email,
        password: password
      });

      if (signInError) {
        console.error('❌ Re-authentication failed:', signInError);

        // If sign-in fails, the user might not exist in Supabase yet
        // Try to create them again via the intermediary server
        console.log('🔄 Sign-in failed, attempting to recreate Supabase user...');
        try {
          const { whopIntermediaryClient } = await import('@/lib/whopIntermediaryClient');
          const supabaseResult = await whopIntermediaryClient.createSupabaseUser(whopUserData);

          if (supabaseResult.success && supabaseResult.data?.credentials) {
            // Try signing in again with the new credentials
            const { data: retrySignInData, error: retrySignInError } = await supabase.auth.signInWithPassword({
              email: supabaseResult.data.credentials.email,
              password: supabaseResult.data.credentials.password
            });

            if (retrySignInError || !retrySignInData.session) {
              console.error('❌ Re-authentication failed even after user recreation:', retrySignInError);
              return false;
            }

            console.log('✅ Whop user re-authenticated successfully after recreation');
            setHasSupabaseSession(true);
            return true;
          } else {
            console.error('❌ Failed to recreate Supabase user during re-authentication:', supabaseResult.error);
            return false;
          }
        } catch (recreateError) {
          console.error('❌ Error recreating Supabase user:', recreateError);
          return false;
        }
      } else if (signInData.session) {
        console.log('✅ Whop user re-authenticated successfully');
        setHasSupabaseSession(true);
        return true;
      } else {
        console.warn('⚠️ Re-authentication succeeded but no session returned');
        return false;
      }
    } catch (error) {
      console.error('❌ Exception during Whop user re-authentication:', error);
      return false;
    }
  };

  const clearWhopAuth = async () => {
    console.log('🗑️ Clearing Whop authentication');
    setIsWhopUser(false);
    setWhopUser(null);
    setAccessResult(null);
    setExperienceId(null);
    setError(null);
    setHasSupabaseSession(false);

    // Clear from localStorage as well including access level
    clearWhopAuthFromStorage();
    localStorage.removeItem('whop_access_level');

    // Clear Supabase session if it exists
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('❌ Error signing out of Supabase:', error);
      } else {
        console.log('✅ Signed out of Supabase');
      }
    } catch (error) {
      console.error('❌ Error during Supabase signout:', error);
    }
  };

  // Function to explicitly set and save experience ID from anywhere in the app
  const setExperienceIdAndSave = (newExperienceId: string) => {
    setExperienceId(newExperienceId);
    saveExperienceIdToStorage(newExperienceId);
  };

  // Initialize on mount
  useEffect(() => {
    // Add error boundary for initialization
    const initializeAuth = async () => {
      try {
        // If we have stored auth but current state shows no Whop user, try to refresh
        const storedAuth = loadWhopAuthFromStorage();
        if (storedAuth.isWhopUser && !isWhopUser) {
          console.log('🔄 Found stored Whop auth, refreshing...');
        }

        // Ensure refreshWhopAuth is a function before calling it
        if (typeof refreshWhopAuth === 'function') {
          await refreshWhopAuth();
        } else {
          console.error('❌ refreshWhopAuth is not a function:', typeof refreshWhopAuth);
          setError('Authentication initialization failed');
          setIsLoading(false);
        }
      } catch (error) {
        console.error('❌ Error during authentication initialization:', error);
        setError('Authentication initialization failed');
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Listen for URL changes to detect experience ID changes
  useEffect(() => {
    const handleLocationChange = () => {
      try {
        const newExperienceId = getExperienceIdFromUrl();

        if (newExperienceId && newExperienceId !== experienceId) {
          // We found a new experience ID in the URL
          console.log('🔄 Experience ID changed, refreshing auth:', {
            old: experienceId,
            new: newExperienceId
          });

          setExperienceId(newExperienceId);
          saveExperienceIdToStorage(newExperienceId);

          // Ensure refreshWhopAuth is a function before calling it
          if (typeof refreshWhopAuth === 'function') {
            refreshWhopAuth();
          } else {
            console.error('❌ refreshWhopAuth is not a function during location change:', typeof refreshWhopAuth);
          }
        } else if (!newExperienceId && experienceId) {
          // We navigated away from an experience URL but keep the stored experience ID
          console.log('🔄 Navigated away from experience URL, keeping stored experience ID:', experienceId);
        }
      } catch (error) {
        console.error('❌ Error handling location change:', error);
      }
    };

    // Listen for popstate events (back/forward navigation)
    window.addEventListener('popstate', handleLocationChange);
    
    // Also check on any navigation (for programmatic navigation)
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;
    
    history.pushState = function(...args) {
      originalPushState.apply(history, args);
      setTimeout(handleLocationChange, 0);
    };
    
    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args);
      setTimeout(handleLocationChange, 0);
    };

    return () => {
      window.removeEventListener('popstate', handleLocationChange);
      history.pushState = originalPushState;
      history.replaceState = originalReplaceState;
    };
  }, [experienceId]);

  // Expose user context to window for iframe SDK access
  useEffect(() => {
    // Expose user context to window for iframe SDK access
    if (typeof window !== 'undefined') {
      (window as any).__WHOP_USER_CONTEXT__ = {
        isWhopUser,
        userId: whopUser?.id || null,
        username: whopUser?.username || null,
        experienceId,
        hasAccess: accessResult?.hasAccess,
        accessLevel: accessResult?.accessLevel,
        isLoading
      };
    }
  }, [isWhopUser, whopUser, accessResult, experienceId, isLoading, error]);

  const value: WhopContextType = {
    isWhopUser,
    whopUser,
    accessResult,
    experienceId,
    isLoading,
    error,
    hasSupabaseSession,
    refreshWhopAuth,
    clearWhopAuth,
    setExperienceIdAndSave
  };

  return (
    <WhopContext.Provider value={value}>
      {children}
    </WhopContext.Provider>
  );
};

export const useWhop = (): WhopContextType => {
  const context = useContext(WhopContext);
  if (context === undefined) {
    throw new Error('useWhop must be used within a WhopProvider');
  }
  return context;
};

// Helper hook for checking if user is a Whop user with access
export const useWhopAccess = () => {
  const { isWhopUser, accessResult, isLoading } = useWhop();

  // All Whop users should have access to the experience
  // Only non-Whop users should be denied access
  const hasAccess = isWhopUser ? true : (accessResult?.hasAccess ?? false);

  // Enhanced access level detection with mobile fallback
  let accessLevel = isWhopUser ? (accessResult?.accessLevel ?? 'customer') : 'no_access';

  // Additional check for mobile admin status preservation
  if (isWhopUser && accessLevel === 'customer') {
    // Check if we have stored admin status that might have been lost on mobile
    const storedAccessLevel = localStorage.getItem('whop_access_level');
    if (storedAccessLevel === 'admin') {
      console.log('📱 Restoring admin access level from storage for mobile user');
      accessLevel = 'admin';
    }
  }

  // Debug logging for Whop access
  React.useEffect(() => {
    if (import.meta.env.DEV) {
      console.group('🏢 useWhopAccess Hook Debug');
      console.log('📊 Access State:', {
        isWhopUser,
        hasAccess,
        accessLevel,
        isAdmin: accessLevel === 'admin',
        isCustomer: accessLevel === 'customer',
        isLoading,
        timestamp: new Date().toISOString()
      });
      console.log('🔍 Access Result Details:', {
        hasAccessResult: !!accessResult,
        accessResultHasAccess: accessResult?.hasAccess,
        accessResultLevel: accessResult?.accessLevel,
        fullAccessResult: accessResult
      });
      console.groupEnd();
    }
  }, [isWhopUser, accessResult, hasAccess, accessLevel, isLoading]);

  return {
    isWhopUser,
    hasAccess,
    accessLevel,
    isAdmin: accessLevel === 'admin',
    isCustomer: accessLevel === 'customer',
    isLoading
  };
};

// Helper hook for getting Whop user info
export const useWhopUser = () => {
  const { isWhopUser, whopUser, isLoading } = useWhop();

  return {
    isWhopUser,
    user: whopUser,
    isLoading,
    username: whopUser?.username,
    email: whopUser?.email,
    profilePicUrl: whopUser?.profilePicUrl
  };
};
