import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Crown,
  DollarSign,
  TrendingUp
} from 'lucide-react';
import { useWhopAccess } from '@/contexts/WhopContext';
import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

interface CompetitionCountdownModalProps {
  isOpen: boolean;
  competitionStartDate: Date;
  onCompetitionStart?: () => void;
}

const CompetitionCountdownModal: React.FC<CompetitionCountdownModalProps> = ({
  isOpen,
  competitionStartDate,
  onCompetitionStart
}) => {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  const [hasStarted, setHasStarted] = useState(false);
  const [communityAnalytics, setCommunityAnalytics] = useState<{
    totalSignups: number;
    totalEarnings: number;
    recentSignups: Array<{
      username: string;
      signupDate: string;
      amount: number;
    }>;
    monthlyStats?: {
      currentMonth: number;
      lastMonth: number;
      growth: number;
    };
  } | null>(null);
  const [isLoadingAnalytics, setIsLoadingAnalytics] = useState(false);

  const { isAdmin, accessLevel } = useWhopAccess();
  const isWhopOwner = isAdmin || accessLevel === 'admin';

  // Load community analytics for Whop owners
  useEffect(() => {
    if (isOpen && isWhopOwner) {
      loadCommunityAnalytics();
    }
  }, [isOpen, isWhopOwner]);

  const loadCommunityAnalytics = async () => {
    if (!isWhopOwner) return;

    setIsLoadingAnalytics(true);
    try {
      const experienceId = new URLSearchParams(window.location.search).get('experienceId') ||
                          window.location.pathname.split('/experiences/')[1];

      if (experienceId) {
        const response = await whopIntermediaryClient.getCommunityAnalytics(experienceId);
        if (response.success && response.data) {
          setCommunityAnalytics(response.data);
        }
      }
    } catch (error) {
      console.error('Failed to load community analytics:', error);
    } finally {
      setIsLoadingAnalytics(false);
    }
  };

  const calculateTimeRemaining = (targetDate: Date): TimeRemaining => {
    const now = new Date().getTime();
    const target = targetDate.getTime();
    const difference = target - now;

    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return { days, hours, minutes, seconds };
  };

  useEffect(() => {
    const interval = setInterval(() => {
      const remaining = calculateTimeRemaining(competitionStartDate);
      setTimeRemaining(remaining);

      const now = new Date();
      if (now >= competitionStartDate && !hasStarted) {
        setHasStarted(true);
        if (onCompetitionStart) {
          onCompetitionStart();
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [competitionStartDate, hasStarted, onCompetitionStart]);

  if (hasStarted) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={() => {}} modal>
      <DialogContent
        className="max-w-[95vw] sm:max-w-4xl lg:max-w-5xl xl:max-w-6xl max-h-[80vh] bg-black border border-white/10 backdrop-blur-xl shadow-[0_8px_32px_rgba(0,0,0,0.4)] text-white focus:outline-none focus:ring-0"
        hideCloseButton
        style={{ outline: 'none', boxShadow: '0 8px 32px rgba(0,0,0,0.4)' }}
      >
        <DialogTitle className="sr-only">Trading Competition Countdown</DialogTitle>
        <DialogDescription className="sr-only">
          Countdown timer showing time remaining until the trading competition begins.
        </DialogDescription>
        <div className="p-3 sm:p-4 lg:p-5">
          {/* Horizontal Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6 items-start">
            
            {/* Left Column - Header */}
            <div>
              <div className="flex items-center gap-3 mb-3">
                <img
                  src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/logos/ChatGPT%20Image%20Jul%2010,%202025,%2006_41_45%20PM.png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJsb2dvcy9DaGF0R1BUIEltYWdlIEp1bCAxMCwgMjAyNSwgMDZfNDFfNDUgUE0ucG5nIiwiaWF0IjoxNzUyMTg3MzYwLCJleHAiOjE3ODM3MjMzNjB9.LO610sLTUtWz6tK1WkUSzsKwWEAHFqduoQV70HkQ2-k"
                  alt="Logo"
                  className="w-8 h-8 sm:w-10 sm:h-10"
                />
                <div className="flex-1">
                  <h1 className="text-sm sm:text-base font-normal text-white leading-tight">
                    Congrats, and welcome to the biggest competition yet.
                  </h1>
                </div>
              </div>
            </div>

            {/* Middle Column - Owner Analytics (if applicable) */}
            {isWhopOwner && (
              <div className="bg-[#141414]/90 border border-white/[0.08] rounded-lg p-3 backdrop-blur-xl">
                <div className="flex items-center gap-2 mb-2">
                  <Crown className="w-3 h-3 text-white/80" />
                  <h3 className="text-xs font-normal text-white">Owner Dashboard</h3>
                </div>

                {isLoadingAnalytics ? (
                  <div className="flex items-center justify-center py-2">
                    <div className="animate-spin rounded-full h-3 w-3 border-2 border-white/10 border-t-white/40"></div>
                  </div>
                ) : communityAnalytics ? (
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="bg-white/[0.02] border border-white/[0.06] rounded p-2">
                      <div className="text-white/60 text-xs">Signups</div>
                      <div className="text-white font-medium">{communityAnalytics.totalSignups}</div>
                    </div>
                    <div className="bg-white/[0.02] border border-white/[0.06] rounded p-2">
                      <div className="text-white/60 text-xs">Earnings</div>
                      <div className="text-white font-medium">${communityAnalytics.totalEarnings.toFixed(0)}</div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-1">
                    <p className="text-white/60 text-xs">Analytics loading...</p>
                  </div>
                )}
              </div>
            )}

            {/* Right Column - Countdown */}
            <div>
              <h3 className="text-xs sm:text-sm font-normal mb-2 text-center text-white">Competition starts in:</h3>
              
              <div className="flex items-center justify-center gap-2">
                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 border border-white/30 rounded flex items-center justify-center mb-1">
                    <motion.span
                      key={timeRemaining.days}
                      initial={{ opacity: 0.6 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0.6 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="text-sm sm:text-base font-normal text-white"
                    >
                      {timeRemaining.days.toString().padStart(2, '0')}
                    </motion.span>
                  </div>
                  <span className="text-xs text-white/60 uppercase">D</span>
                </div>

                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 border border-white/30 rounded flex items-center justify-center mb-1">
                    <motion.span
                      key={timeRemaining.hours}
                      initial={{ opacity: 0.6 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0.6 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="text-sm sm:text-base font-normal text-white"
                    >
                      {timeRemaining.hours.toString().padStart(2, '0')}
                    </motion.span>
                  </div>
                  <span className="text-xs text-white/60 uppercase">H</span>
                </div>

                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 border border-white/30 rounded flex items-center justify-center mb-1">
                    <motion.span
                      key={timeRemaining.minutes}
                      initial={{ opacity: 0.6 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0.6 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="text-sm sm:text-base font-normal text-white"
                    >
                      {timeRemaining.minutes.toString().padStart(2, '0')}
                    </motion.span>
                  </div>
                  <span className="text-xs text-white/60 uppercase">M</span>
                </div>

                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 border border-white/30 rounded flex items-center justify-center mb-1">
                    <motion.span
                      key={timeRemaining.seconds}
                      initial={{ opacity: 0.6 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0.6 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="text-sm sm:text-base font-normal text-white"
                    >
                      {timeRemaining.seconds.toString().padStart(2, '0')}
                    </motion.span>
                  </div>
                  <span className="text-xs text-white/60 uppercase">S</span>
                </div>
              </div>
            </div>

          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CompetitionCountdownModal;
