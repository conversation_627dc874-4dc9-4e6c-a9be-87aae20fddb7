import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  Crown,
  DollarSign,
  TrendingUp
} from 'lucide-react';
import { useWhopAccess } from '@/contexts/WhopContext';
import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

interface CompetitionCountdownModalProps {
  isOpen: boolean;
  competitionStartDate: Date;
  onCompetitionStart?: () => void;
}

const CompetitionCountdownModal: React.FC<CompetitionCountdownModalProps> = ({
  isOpen,
  competitionStartDate,
  onCompetitionStart
}) => {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  const [hasStarted, setHasStarted] = useState(false);
  const [communityAnalytics, setCommunityAnalytics] = useState<{
    totalSignups: number;
    totalEarnings: number;
    recentSignups: Array<{
      username: string;
      signupDate: string;
      amount: number;
    }>;
    monthlyStats?: {
      currentMonth: number;
      lastMonth: number;
      growth: number;
    };
  } | null>(null);
  const [isLoadingAnalytics, setIsLoadingAnalytics] = useState(false);

  const { isAdmin, accessLevel } = useWhopAccess();
  const isWhopOwner = isAdmin || accessLevel === 'admin';

  // Load community analytics for Whop owners
  useEffect(() => {
    if (isOpen && isWhopOwner) {
      loadCommunityAnalytics();
    }
  }, [isOpen, isWhopOwner]);

  const loadCommunityAnalytics = async () => {
    if (!isWhopOwner) return;

    setIsLoadingAnalytics(true);
    try {
      const experienceId = new URLSearchParams(window.location.search).get('experienceId') ||
                          window.location.pathname.split('/experiences/')[1];

      if (experienceId) {
        const response = await whopIntermediaryClient.getCommunityAnalytics(experienceId);
        if (response.success && response.data) {
          setCommunityAnalytics(response.data);
        }
      }
    } catch (error) {
      console.error('Failed to load community analytics:', error);
    } finally {
      setIsLoadingAnalytics(false);
    }
  };

  const calculateTimeRemaining = (targetDate: Date): TimeRemaining => {
    const now = new Date().getTime();
    const target = targetDate.getTime();
    const difference = target - now;

    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return { days, hours, minutes, seconds };
  };

  useEffect(() => {
    const interval = setInterval(() => {
      const remaining = calculateTimeRemaining(competitionStartDate);
      setTimeRemaining(remaining);

      const now = new Date();
      if (now >= competitionStartDate && !hasStarted) {
        setHasStarted(true);
        if (onCompetitionStart) {
          onCompetitionStart();
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [competitionStartDate, hasStarted, onCompetitionStart]);

  if (hasStarted) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={() => {}} modal>
      <DialogContent
        className="max-w-[95vw] sm:max-w-4xl lg:max-w-5xl xl:max-w-6xl max-h-[80vh] bg-[#1a1a1a] border border-white/10 backdrop-blur-xl shadow-[0_8px_32px_rgba(0,0,0,0.4)] text-white focus:outline-none focus:ring-0"
        hideCloseButton
        style={{ outline: 'none', boxShadow: '0 8px 32px rgba(0,0,0,0.4)' }}
      >
        <DialogTitle className="sr-only">Trading Competition Countdown</DialogTitle>
        <DialogDescription className="sr-only">
          Countdown timer showing time remaining until the trading competition begins.
        </DialogDescription>
        <div className="p-6 sm:p-8 lg:p-10">
          {/* Top Section - Congrats Header */}
          <div className="text-center mb-8">
            <div className="flex justify-center items-center gap-4 mb-4">
              <img
                src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/logos/ChatGPT%20Image%20Jul%2010,%202025,%2006_41_45%20PM.png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJsb2dvcy9DaGF0R1BUIEltYWdlIEp1bCAxMCwgMjAyNSwgMDZfNDFfNDUgUE0ucG5nIiwiaWF0IjoxNzUyODA4NDczLCJleHAiOjE3ODQzNDQ0NzN9.xMhtKSQzoh0MN4s0Ybi4BQhSToGWQGZCBQblfN8Lnas"
                alt="TradeOff Logo"
                className="w-12 h-12 sm:w-16 sm:h-16 object-contain"
                onError={(e) => {
                  console.error('Logo failed to load:', e);
                  e.currentTarget.style.display = 'none';
                }}
              />
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-normal text-white leading-tight">
                Congrats, and welcome to the biggest competition yet.
              </h1>
            </div>
          </div>

          {/* Main Content - Cleaner Three Section Layout */}
          <div className="space-y-8">

            {/* Top Section - Owner Analytics (if applicable) */}
            {isWhopOwner && (
              <div className="bg-[#2a2a2a]/80 border border-white/[0.12] rounded-lg p-6 backdrop-blur-xl">
                <div className="flex items-center gap-3 mb-5">
                  <Crown className="w-6 h-6 text-white/90" />
                  <h3 className="text-lg font-normal text-white">Owner Dashboard</h3>
                </div>

                {isLoadingAnalytics ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-2 border-white/15 border-t-white/50"></div>
                  </div>
                ) : communityAnalytics ? (
                  <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
                    <div className="bg-[#3a3a3a]/60 border border-white/[0.1] rounded-lg p-4 text-center">
                      <div className="text-white/70 text-sm mb-1">Signups</div>
                      <div className="text-white font-medium text-2xl">{communityAnalytics.totalSignups}</div>
                    </div>
                    <div className="bg-[#3a3a3a]/60 border border-white/[0.1] rounded-lg p-4 text-center">
                      <div className="text-white/70 text-sm mb-1">Earnings</div>
                      <div className="text-white font-medium text-2xl">${communityAnalytics.totalEarnings.toFixed(0)}</div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <p className="text-white/70 text-lg">Analytics loading...</p>
                  </div>
                )}
              </div>
            )}

            {/* Middle Section - Countdown */}
            <div className="text-center">
              <h3 className="text-lg sm:text-xl font-normal mb-6 text-white">Competition starts in:</h3>

              <div className="flex items-center justify-center gap-4 mb-8">
                <div className="flex flex-col items-center">
                  <div className="w-14 h-14 sm:w-16 sm:h-16 border-2 border-white/30 rounded-lg flex items-center justify-center mb-2">
                    <motion.span
                      key={timeRemaining.days}
                      initial={{ opacity: 0.6 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0.6 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="text-xl sm:text-2xl font-normal text-white"
                    >
                      {timeRemaining.days.toString().padStart(2, '0')}
                    </motion.span>
                  </div>
                  <span className="text-sm text-white/60 uppercase font-medium">Days</span>
                </div>

                <div className="flex flex-col items-center">
                  <div className="w-14 h-14 sm:w-16 sm:h-16 border-2 border-white/30 rounded-lg flex items-center justify-center mb-2">
                    <motion.span
                      key={timeRemaining.hours}
                      initial={{ opacity: 0.6 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0.6 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="text-xl sm:text-2xl font-normal text-white"
                    >
                      {timeRemaining.hours.toString().padStart(2, '0')}
                    </motion.span>
                  </div>
                  <span className="text-sm text-white/60 uppercase font-medium">Hours</span>
                </div>

                <div className="flex flex-col items-center">
                  <div className="w-14 h-14 sm:w-16 sm:h-16 border-2 border-white/30 rounded-lg flex items-center justify-center mb-2">
                    <motion.span
                      key={timeRemaining.minutes}
                      initial={{ opacity: 0.6 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0.6 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="text-xl sm:text-2xl font-normal text-white"
                    >
                      {timeRemaining.minutes.toString().padStart(2, '0')}
                    </motion.span>
                  </div>
                  <span className="text-sm text-white/60 uppercase font-medium">Minutes</span>
                </div>

                <div className="flex flex-col items-center">
                  <div className="w-14 h-14 sm:w-16 sm:h-16 border-2 border-white/30 rounded-lg flex items-center justify-center mb-2">
                    <motion.span
                      key={timeRemaining.seconds}
                      initial={{ opacity: 0.6 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0.6 }}
                      transition={{ duration: 0.3, ease: "easeInOut" }}
                      className="text-xl sm:text-2xl font-normal text-white"
                    >
                      {timeRemaining.seconds.toString().padStart(2, '0')}
                    </motion.span>
                  </div>
                  <span className="text-sm text-white/60 uppercase font-medium">Seconds</span>
                </div>
              </div>
            </div>

            {/* Bottom Section - Competition Details */}
            <div className="bg-[#2a2a2a]/80 border border-white/[0.12] rounded-lg p-6 backdrop-blur-xl">
              <h4 className="text-lg font-normal text-white mb-4 text-center">Competition Details</h4>
              <div className="space-y-3 text-white/80 max-w-md mx-auto">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>$100,000 starting balance for all participants</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>Top #3 Traders win prizes</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>The competition starts when the countdown is at 0. Goodluck.</span>
                </div>
              </div>
            </div>

          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CompetitionCountdownModal;
