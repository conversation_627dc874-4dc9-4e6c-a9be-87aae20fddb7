import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  TrendingUp,
  BarChart3,
  Target,
  Shield,
  Trophy,
  Clock,
  CheckCircle,
  ArrowRight,
  Sparkles,
  Crown,
  DollarSign
} from 'lucide-react';
import { useIframeSdk } from '@/hooks/useIframeSdk';
import { whopIntermediaryClient } from '@/lib/whopIntermediaryClient';
import { useToast } from '@/components/ui/use-toast';
import { useWhopAccess } from '@/contexts/WhopContext';

interface TradingOnboardingModalProps {
  isOpen: boolean;
  onComplete: () => void;
}

const TradingOnboardingModal: React.FC<TradingOnboardingModalProps> = ({
  isOpen,
  onComplete
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);


  const iframeSdk = useIframeSdk();
  const { toast } = useToast();
  const { isAdmin, accessLevel } = useWhopAccess();

  const totalSteps = 2;
  const isWhopOwner = isAdmin || accessLevel === 'admin';

  // Debug Whop access and ownership detection
  React.useEffect(() => {
    console.group('🎯 Trading Onboarding Modal - Whop Access Debug');
    console.log('🔑 Whop Access Info:', {
      isAdmin,
      accessLevel,
      isWhopOwner,
      calculation: `${isAdmin} || ${accessLevel} === 'admin' = ${isWhopOwner}`,
      timestamp: new Date().toISOString()
    });
    console.groupEnd();
  }, [isAdmin, accessLevel, isWhopOwner]);



  const features = [
    {
      icon: <TrendingUp className="h-5 w-5" />,
      title: "Real-Time Paper Trading",
      description: "Practice with live market data without risking real money"
    },
    {
      icon: <BarChart3 className="h-5 w-5" />,
      title: "Advanced Charts",
      description: "Professional-grade charts with technical indicators"
    },
    {
      icon: <Target className="h-5 w-5" />,
      title: "Win Prizes",
      description: "Win big prizes and test your skills against the best"
    },
    {
      icon: <Trophy className="h-5 w-5" />,
      title: "Trading Competition",
      description: "Compete with other traders in live competitions"
    }
  ];



  const handlePayment = async () => {
    // Skip payment for Whop owners
    if (isWhopOwner) {
      console.log('👑 Whop owner detected, skipping payment and completing onboarding');
      toast({
        title: "Welcome, Community Owner!",
        description: "You have free access to the trading competition. Get ready to trade!",
      });
      onComplete();
      return;
    }

    if (!iframeSdk) {
      setPaymentError('Payment system not available');
      return;
    }

    setIsProcessingPayment(true);
    setPaymentError(null);

    try {
      console.log('💳 Starting Whop payment flow for trading competition...');

      // Get experience ID for affiliate tracking
      const experienceId = new URLSearchParams(window.location.search).get('experienceId') ||
                          window.location.pathname.split('/experiences/')[1];

      // Create charge via intermediary server with affiliate metadata
      const chargeResponse = await whopIntermediaryClient.createCharge(
        10, // $10.00 - competition entry fee (in dollars, not cents)
        'usd',
        'TradeOff Trading Competition Entry',
        {
          experienceId,
          affiliatePayoutAmount: 1, // $1 affiliate payout
          isAffiliateEligible: true
        }
      );

      console.log('📡 Charge creation response:', chargeResponse);

      if (!chargeResponse.success || !chargeResponse.data?.inAppPurchase) {
        throw new Error(chargeResponse.error || 'Failed to create charge');
      }

      // Open Whop payment modal
      console.log('🖼️ Opening Whop payment modal...');
      const paymentResult = await iframeSdk.inAppPurchase(chargeResponse.data.inAppPurchase);

      console.log('💳 Payment result:', paymentResult);

      if (paymentResult?.status === "ok") {
        console.log('✅ Payment successful, processing affiliate payout...');

        // Process affiliate payout if we have the necessary data
        if (experienceId && paymentResult.data?.receiptId) {
          try {
            await processAffiliatePayout(experienceId, paymentResult.data.receiptId, paymentResult.data.sessionId);
          } catch (affiliateError) {
            console.error('⚠️ Affiliate payout failed, but payment was successful:', affiliateError);
            // Don't fail the main flow if affiliate payout fails
          }
        }

        toast({
          title: "Welcome to the Trading Competition!",
          description: "Your entry has been confirmed. Get ready to trade!",
        });
        onComplete();
      } else {
        throw new Error(paymentResult?.error || 'Payment was not completed');
      }
    } catch (error) {
      console.error('❌ Payment error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      setPaymentError(errorMessage);
      toast({
        title: "Payment Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const processAffiliatePayout = async (experienceId: string, receiptId: string, userId: string) => {
    try {
      console.log('💰 Processing affiliate payout...', { experienceId, receiptId, userId });

      // Get the community owner's username from the experience
      // This would typically be done server-side, but for now we'll use a placeholder
      const ownerUsername = 'community_owner'; // This should be fetched from the experience data

      const payoutResponse = await whopIntermediaryClient.sendAffiliatePayout(
        ownerUsername,
        1, // $1 affiliate payout
        experienceId
      );

      if (payoutResponse.success) {
        console.log('✅ Affiliate payout sent successfully:', payoutResponse);
      } else {
        throw new Error(payoutResponse.error || 'Failed to send affiliate payout');
      }
    } catch (error) {
      console.error('❌ Affiliate payout failed:', error);
      throw error;
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const renderStep1 = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="relative"
    >


      {/* Centered header layout for first impression */}
      <div className="text-center mb-4 sm:mb-8">
        {/* Logo centered above title */}
        <div className="flex justify-center mb-3 sm:mb-6">
          <div className="relative">
            <img
              src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/logos/ChatGPT%20Image%20Jul%2010,%202025,%2006_41_45%20PM.png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJsb2dvcy9DaGF0R1BUIEltYWdlIEp1bCAxMCwgMjAyNSwgMDZfNDFfNDUgUE0ucG5nIiwiaWF0IjoxNzUyMjAyMzc4LCJleHAiOjE3ODM3MzgzNzh9.3AARqJ-yZiN6WlYQ-Z8SZ3Pnwgur1FBR7A3h6_FMGEQ"
              alt="Logo"
              className="w-12 h-12 sm:w-20 sm:h-20 object-contain filter drop-shadow-lg"
            />
            {/* Subtle glow effect around logo */}
            <div className="absolute inset-0 w-12 h-12 sm:w-20 sm:h-20 bg-gradient-to-br from-white/5 to-transparent rounded-full blur-xl" />
          </div>
        </div>

        {/* Large, prominent title with refined gradient */}
        <h1 className="text-xl sm:text-4xl font-semibold bg-gradient-to-br from-white via-gray-50 to-gray-200 bg-clip-text text-transparent leading-tight mb-2 sm:mb-4 drop-shadow-sm"
            style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif', letterSpacing: '-0.02em' }}>
          Welcome to TradeOff
        </h1>

        {/* Subtitle with subtle gradient */}
        <p className="bg-gradient-to-r from-white/80 via-white/70 to-white/60 bg-clip-text text-transparent text-sm sm:text-lg leading-relaxed"
           style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
          Compete in trading competitions
        </p>
      </div>

      {/* Enhanced Get Started Button with detailed styling */}
      <div className="flex justify-center pt-4 sm:pt-8">
        <div className="relative">
          {/* Subtle outer glow */}
          <div className="absolute -inset-1 bg-gradient-to-r from-white/10 via-white/20 to-white/10 rounded-lg blur-sm opacity-60"></div>

          <button
            onClick={nextStep}
            className="group relative bg-gradient-to-b from-white to-gray-50 text-black font-medium py-2.5 sm:py-3 px-5 sm:px-8 text-sm sm:text-base rounded-lg border border-white/[0.12] transition-all duration-200 ease-out hover:from-gray-50 hover:to-gray-100 active:from-gray-100 active:to-gray-200 shadow-[0_1px_3px_rgba(0,0,0,0.1),inset_0_1px_0_rgba(255,255,255,0.8),inset_0_-1px_0_rgba(0,0,0,0.05)] hover:shadow-[0_2px_8px_rgba(0,0,0,0.15),inset_0_1px_0_rgba(255,255,255,0.9),inset_0_-1px_0_rgba(0,0,0,0.08)] focus:outline-none focus:ring-0"
            style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}
          >
            <span className="relative z-10 bg-gradient-to-b from-gray-900 to-black bg-clip-text text-transparent font-semibold">Get Started</span>

            {/* Refined inner background transition */}
            <div className="absolute inset-0 rounded-lg bg-gradient-to-b from-gray-50 to-gray-100 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ease-out" />

            {/* Subtle inner highlight */}
            <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/60 to-transparent rounded-t-lg" />
          </button>
        </div>
      </div>
    </motion.div>
  );

  const renderStep2 = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-3 sm:space-y-4"
    >
      {/* Horizontal Layout for larger screens */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 items-start">
        {/* Left Column - Header and Features */}
        <div className="lg:pr-4">
          {/* Green Success Checkmark and Header */}
          <div className="flex items-center gap-3 mb-4 lg:mb-6">
            <div className="relative flex-shrink-0">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-500/20 border-2 border-green-500/40 rounded-full flex items-center justify-center">
                <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-400" />
              </div>
              <div className="absolute inset-0 w-8 h-8 sm:w-10 sm:h-10 bg-green-400/10 rounded-full blur-xl" />
            </div>
            <div className="text-left flex-1">
              <div className="flex items-start justify-between gap-4">
                <div>
                  <h2 className="text-base sm:text-xl font-normal text-white mb-1">
                    Join the Competition
                  </h2>
                  <p className="text-white/70 text-xs sm:text-sm">
                    Go up against the best and compete for prizes.
                  </p>
                </div>
                {/* Free access indicator for owners - moved to right with proper spacing */}
                {isWhopOwner && (
                  <div className="bg-green-500/[0.08] border border-green-500/[0.15] rounded-md px-2 py-1 flex items-center gap-1 flex-shrink-0">
                    <span className="text-green-400/90 text-xs font-medium">Free to Whop owners</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Features List with Better Spacing */}
          <div className="space-y-4 mb-8">
            {features.map((feature, index) => (
              <div key={index} className="flex items-center gap-4 p-4 rounded-lg bg-[#0f0f0f] border border-white/[0.04] shadow-[inset_0_1px_0_rgba(255,255,255,0.02)]">
                <div className="w-2 h-2 bg-green-400 rounded-full flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  <span className="text-white/90 text-sm sm:text-base font-normal">{feature.title}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Right Column - Pricing/Owner Info */}
        <div className="lg:pl-4">
          {/* Competition Entry Pricing or Owner Dashboard */}
          {isWhopOwner ? (
        <div className="bg-[#0a0a0a] border border-white/[0.06] rounded-lg p-6 sm:p-8 shadow-[inset_0_1px_0_rgba(255,255,255,0.02)]">
          <div className="flex items-center gap-4 mb-6">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-white/[0.03] border border-white/[0.08] rounded-lg flex items-center justify-center">
              <Crown className="w-5 h-5 sm:w-6 sm:h-6 text-white/90" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg sm:text-xl font-medium text-white/95 leading-tight" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                Community Owner Access
              </h3>
              <p className="text-white/60 text-sm sm:text-base mt-1 leading-relaxed">Exclusive owner benefits</p>
            </div>
          </div>

          {/* Owner Benefits - Column Layout with Better Spacing */}
          <div className="space-y-4 mb-8">
            <div className="flex items-center gap-4 p-4 bg-[#0f0f0f] border border-white/[0.04] rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.01)]">
              <div className="w-6 h-6 bg-white/[0.03] border border-white/[0.06] rounded-md flex items-center justify-center flex-shrink-0">
                <BarChart3 className="w-3.5 h-3.5 text-white/80" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm sm:text-base font-medium text-white/90 leading-relaxed" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                  Track on dashboard
                </div>
                <div className="text-xs sm:text-sm text-white/50 leading-relaxed mt-1">Monitor community growth</div>
              </div>
            </div>

            <div className="flex items-center gap-4 p-4 bg-[#0f0f0f] border border-white/[0.04] rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.01)]">
              <div className="w-6 h-6 bg-white/[0.03] border border-white/[0.06] rounded-md flex items-center justify-center flex-shrink-0">
                <TrendingUp className="w-3.5 h-3.5 text-white/80" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm sm:text-base font-medium text-white/90 leading-relaxed" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                  Boost Whop activity
                </div>
                <div className="text-xs sm:text-sm text-white/50 leading-relaxed mt-1">Drive more participation</div>
              </div>
            </div>

            <div className="flex items-center gap-4 p-4 bg-[#0f0f0f] border border-white/[0.04] rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.01)]">
              <div className="w-6 h-6 bg-green-500/[0.08] border border-green-500/[0.15] rounded-md flex items-center justify-center flex-shrink-0">
                <DollarSign className="w-3.5 h-3.5 text-green-400/90" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm sm:text-base font-medium text-white/90 leading-relaxed" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                  Earn 20% commission
                </div>
                <div className="text-xs sm:text-sm text-white/50 leading-relaxed mt-1">Per community signup</div>
              </div>
            </div>
          </div>

          {/* Button at bottom left of owner card */}
          <div className="flex justify-start pt-2">
            <button
              onClick={handlePayment}
              disabled={isProcessingPayment}
              className="group relative bg-white text-black font-medium py-3 px-8 text-base rounded-lg border border-white/[0.08] transition-all duration-200 ease-out hover:bg-white/95 active:bg-white/90 shadow-[0_1px_3px_rgba(0,0,0,0.1)] hover:shadow-[0_2px_8px_rgba(0,0,0,0.15)] disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-0"
              style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}
            >
              {isProcessingPayment ? (
                <span className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-black/20 border-t-black mr-2"></div>
                  <span className="text-base">Processing...</span>
                </span>
              ) : (
                <span className="relative z-10">
                  Enter Competition
                </span>
              )}
              {/* Subtle inner background transition */}
              {!isProcessingPayment && (
                <div className="absolute inset-0 rounded-lg bg-white/95 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ease-out" />
              )}
            </button>
          </div>
        </div>
      ) : (
        <div className="bg-[#0a0a0a] border border-white/[0.06] rounded-lg p-6 sm:p-8 shadow-[inset_0_1px_0_rgba(255,255,255,0.02)]">
          <div className="flex items-center justify-between mb-6 sm:mb-8">
            <h3 className="text-lg sm:text-xl font-medium text-white/95">Competition Entry</h3>
            <span className="bg-[#0f0f0f] text-white/90 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-sm sm:text-base border border-white/[0.06] font-medium">
              $10
            </span>
          </div>

          <div className="pt-4 sm:pt-6 border-t border-white/[0.08] mb-6 sm:mb-8">
            <div className="flex items-center justify-between">
              <span className="text-white/60 text-base sm:text-lg">Entry Fee:</span>
              <span className="text-xl sm:text-2xl font-semibold text-white/95">$10.00</span>
            </div>
          </div>

          {/* Error handling for non-owners */}
          {paymentError && (
            <div className="bg-red-500/[0.08] border border-red-500/[0.15] rounded-lg p-4 sm:p-5 mb-6">
              <p className="text-red-300/90 text-base sm:text-lg">{paymentError}</p>
            </div>
          )}

          {/* Button for non-owners */}
          <div className="flex justify-start">
            <button
              onClick={handlePayment}
              disabled={isProcessingPayment}
              className="group relative bg-white text-black font-medium py-3 sm:py-4 px-8 sm:px-10 text-base sm:text-lg rounded-lg border border-white/[0.08] transition-all duration-200 ease-out hover:bg-white/95 active:bg-white/90 shadow-[0_1px_3px_rgba(0,0,0,0.1)] hover:shadow-[0_2px_8px_rgba(0,0,0,0.15)] disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-0"
              style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}
            >
              {isProcessingPayment ? (
                <span className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 sm:h-5 w-4 sm:w-5 border-2 border-black/20 border-t-black mr-2"></div>
                  <span className="text-base sm:text-lg">Processing...</span>
                </span>
              ) : (
                <span className="relative z-10">
                  Join Competition
                </span>
              )}
              {/* Subtle inner background transition */}
              {!isProcessingPayment && (
                <div className="absolute inset-0 rounded-lg bg-white/95 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ease-out" />
              )}
            </button>
          </div>

          </div>
          )}
        </div>
      </div>
    </motion.div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={() => {}} modal>
      <DialogContent
        className="max-w-[95vw] sm:max-w-md lg:max-w-lg xl:max-w-xl max-h-[80vh] w-full bg-[#0a0a0a] border border-white/[0.08] text-white rounded-xl focus:outline-none focus:ring-0 shadow-[0_20px_60px_rgba(0,0,0,0.4),inset_0_1px_0_rgba(255,255,255,0.03),inset_0_-1px_0_rgba(0,0,0,0.2)]"
        hideCloseButton
        style={{ outline: 'none', boxShadow: '0 20px 60px rgba(0,0,0,0.4), inset 0 1px 0 rgba(255,255,255,0.03), inset 0 -1px 0 rgba(0,0,0,0.2)' }}
      >
        <DialogTitle className="sr-only">Trading Competition Onboarding</DialogTitle>
        <DialogDescription className="sr-only">
          Complete your onboarding to join the trading competition. This includes payment verification and access setup.
        </DialogDescription>
        <div className="p-3 sm:p-4 lg:p-5">
          <AnimatePresence mode="wait">
            {currentStep === 1 && renderStep1()}
            {currentStep === 2 && renderStep2()}
          </AnimatePresence>


        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TradingOnboardingModal;
