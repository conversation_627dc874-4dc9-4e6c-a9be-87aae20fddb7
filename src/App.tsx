import React, { useState, useEffect } from "react";
import { setRefreshTokenFunction } from "./utils/tokenUtils";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from "react-router-dom";
// Using custom iframe SDK implementation
import './lib/iframe-sdk'; // Initialize the iframe SDK

import AppLayout from "./components/layout/AppLayout";
import { supabase } from "./integrations/supabase/client";
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { GamificationProvider } from './contexts/GamificationContext';
import { WatchlistProvider } from './contexts/WatchlistContext';
import { WhopProvider } from './contexts/WhopContext';
import { useUnifiedAuth } from './hooks/useUnifiedAuth';
import GamificationWrapper from './components/gamification/GamificationWrapper';
import { cn } from "@/lib/utils";

import LandingPage from './pages/LandingPage';

// Import lazy routes and preloading service for performance optimization
import { LazyRoutes, preloadCommonRoutes } from './components/routing/LazyRoutes';
import { preloadingService } from './services/preloadingService';

import { initializeGrowiTracking } from './utils/growiLoader';
import { testEnvironmentVariables } from './utils/envTest';
import MobileAwareRoute from './components/mobile/MobileAwareRoute';
import WhopExperience from './pages/WhopExperience';

import { initWhopDevTools } from './utils/whopDev';
// Import user info utils to make them globally available
import { printUserInfo, gatherUserInfo } from './utils/userInfoUtils';



// Protected route component that works with both regular and Whop users
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, userType } = useUnifiedAuth();
  const location = useLocation();
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  useEffect(() => {
    // Give a short delay to ensure auth state is loaded
    const timer = setTimeout(() => {
      setIsCheckingAuth(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Show loading state while checking authentication
  if (isCheckingAuth) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white/30"></div>
      </div>
    );
  }

  // Redirect if not authenticated after checking
  if (!isAuthenticated) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }


  return <>{children}</>;
};

// Create a wrapper component to handle the auth modal logic
const AppContent = () => {
  const { refreshToken } = useAuth();
  const { isAuthenticated } = useUnifiedAuth(); // Use unified auth to support both regular and Whop users
  const location = useLocation();

  // Set the refreshToken function from AuthContext (only once)
  useEffect(() => {
    if (refreshToken) {
      setRefreshTokenFunction(refreshToken);
    }
  }, []); // Remove refreshToken dependency to prevent excessive calls

  // Initialize Growi tracking and Whop dev tools on app load
  useEffect(() => {
    // Test environment variables first
    const envTest = testEnvironmentVariables();

    initializeGrowiTracking();

    // Initialize Whop development tools in development mode
    if (import.meta.env.DEV) {
      initWhopDevTools();
    }

    // Expose user info functions globally
    if (typeof window !== 'undefined') {
      (window as any).printUserInfo = printUserInfo;
      (window as any).gatherUserInfo = gatherUserInfo;
    }
  }, []);

  // Define paths where auth modal should not appear
  const noAuthModalPaths = ['/terms', '/privacy', '/subscription', '/subscription/manage'];

  // Check authentication status
  const isUserAuthenticated = isAuthenticated;









  useEffect(() => {
    // Check for auth tokens in the URL (from OAuth redirects)
    const checkForAuthTokens = async () => {
      try {
        // Check if we have an access token in the URL hash (from OAuth)
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const accessToken = hashParams.get('access_token');

        if (accessToken) {
          // This will be handled by AuthContext now
        }
      } catch (error) {
      }
    };

    checkForAuthTokens();
  }, [location.pathname]);

  // Add visibility change handler
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // Only reconnect Supabase auth - no query invalidation
        supabase.auth.startAutoRefresh();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Show landing page first if not authenticated
  if (location.pathname === '/' && !isUserAuthenticated) {
    return <LandingPage />;
  }

  return (
    <div className={cn(
      "bg-[#0A0A0A]",
      // Only fix position when not on terms/privacy pages
      !noAuthModalPaths.includes(location.pathname) ? "fixed inset-0 overflow-hidden" : "min-h-screen"
    )}>
      <GamificationProvider>
        <GamificationWrapper>
          <Routes>
              {/* Whop Experience Routes - these must come first */}
              <Route path="/experiences/:experienceId" element={<WhopExperience />} />

              {/* Full-screen routes without sidebar - these must come first */}
              <Route path="/backtest-results" element={<ProtectedRoute><LazyRoutes.BacktestResults /></ProtectedRoute>} />
              {/* Trading routes - standalone Whop app, no regular auth required */}
              <Route path="/trade" element={<LazyRoutes.Trading />} />
              <Route path="/trading" element={<LazyRoutes.Trading />} />

              {/* Routes with AppLayout and sidebar */}
              <Route path="/home" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.Home /></ProtectedRoute></AppLayout>} />
              <Route path="/settings" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.Settings /></ProtectedRoute></AppLayout>} />
              <Route path="/model-settings" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.ModelSettings /></ProtectedRoute></AppLayout>} />
              <Route path="/subscription" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.Subscription /></ProtectedRoute></AppLayout>} />
              <Route path="/subscription/manage" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.ManageSubscription /></ProtectedRoute></AppLayout>} />
              <Route path="/trades" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.Trades /></ProtectedRoute></AppLayout>} />
              <Route path="/portfolio-builder" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.PortfolioManager /></ProtectedRoute></AppLayout>} />
              <Route path="/portfolio-manager" element={<Navigate to="/portfolio-builder" replace />} />
              <Route path="/portfolio-news" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.PortfolioNews /></ProtectedRoute></AppLayout>} />
              <Route path="/api-test" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.ApiTest /></ProtectedRoute></AppLayout>} />

              <Route path="/agents" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.AgentManagement /></ProtectedRoute></AppLayout>} />
              <Route path="/agent-library" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.AgentManagement /></ProtectedRoute></AppLayout>} />
              <Route path="/agent-builder" element={<AppLayout loading={false}><ProtectedRoute><MobileAwareRoute><LazyRoutes.AgentManagement /></MobileAwareRoute></ProtectedRoute></AppLayout>} />
              <Route path="/agent-builder/new" element={<AppLayout loading={false}><ProtectedRoute><MobileAwareRoute><LazyRoutes.AgentBuilder /></MobileAwareRoute></ProtectedRoute></AppLayout>} />
              <Route path="/agent-builder/:id" element={<AppLayout loading={false}><ProtectedRoute><MobileAwareRoute><LazyRoutes.AgentBuilder /></MobileAwareRoute></ProtectedRoute></AppLayout>} />
              <Route path="/discover" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.Discover /></ProtectedRoute></AppLayout>} />
              <Route path="/agent-scanner" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.AgentScanner /></ProtectedRoute></AppLayout>} />
              <Route path="/agent-backtesting" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.AgentBacktesting /></ProtectedRoute></AppLayout>} />
              <Route path="/agent-builder-docs" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.AgentBuilderDocs /></ProtectedRoute></AppLayout>} />
              <Route path="/stock-search" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.StockSearch /></ProtectedRoute></AppLayout>} />
              <Route path="/stock/:symbol" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.StockDetail /></ProtectedRoute></AppLayout>} />
              <Route path="/stripe-test" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.StripeTest /></ProtectedRoute></AppLayout>} />


              <Route path="/stock-screener" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.Screener /></ProtectedRoute></AppLayout>} />
              <Route path="/stock-screener/:symbol" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.Screener /></ProtectedRoute></AppLayout>} />
              <Route path="/chart-generator" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.ChartGenerator /></ProtectedRoute></AppLayout>} />
              <Route path="/trading-demo" element={<AppLayout loading={false}><ProtectedRoute><LazyRoutes.TradingDemo /></ProtectedRoute></AppLayout>} />

              {/* Routes without AppLayout */}
              <Route path="/about" element={<LazyRoutes.About />} />
              <Route path="/terms" element={<LazyRoutes.TermsOfService />} />
              <Route path="/privacy" element={<LazyRoutes.PrivacyPolicy />} />

              {/* Whop OAuth callback - no auth required */}
              <Route path="/whop/callback" element={<LazyRoutes.WhopCallback />} />

              <Route path="/login" element={<Navigate to="/" replace />} />

              {/* Default route - redirect to home (headquarters) */}
              <Route path="/" element={<Navigate to="/home" replace />} />
              <Route path="*" element={<Navigate to="/home" replace />} />
          </Routes>
        </GamificationWrapper>
      </GamificationProvider>





      {/* Debug Components - only in development */}
      {import.meta.env.DEV && (
        <>
        </>
      )}
    </div>
  );
};

const App = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: 1,
        staleTime: 5 * 60 * 1000,
        refetchOnWindowFocus: false,
        refetchOnReconnect: false,
      },
    },
  });

  // Initialize performance optimizations and Whop SDK logging
  useEffect(() => {
    // Start preloading common routes after initial render
    preloadCommonRoutes();

    // Preload critical resources
    preloadingService.preloadCriticalResources();

    // Set up route-based prefetching
    const handleRouteChange = () => {
      const currentPath = window.location.pathname;
      preloadingService.prefetchRouteData(currentPath);
    };

    // Listen for route changes
    window.addEventListener('popstate', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <WhopProvider>
          <WatchlistProvider>
            <TooltipProvider>
              <Toaster />
              <Sonner />
              <Router
                future={{
                  v7_startTransition: true,
                  v7_relativeSplatPath: true
                }}
              >
                <AppContent />
              </Router>
            </TooltipProvider>
          </WatchlistProvider>
        </WhopProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};

export default App;
