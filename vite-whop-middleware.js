/**
 * Vite middleware for handling Whop proxy requests
 * This middleware intercepts requests to /api/whop-proxy and forwards them to the intermediary server
 */

// Use environment-specific URL with fallback to localhost for development
function getIntermediaryServerUrl() {
  // Check if we're in development mode
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Use localhost for development, production URL for production
  if (isDevelopment) {
    return 'http://localhost:3001';
  }

  return process.env.VITE_WHOP_INTERMEDIARY_URL || 'https://whop-intermediary-server.vercel.app';
}

const INTERMEDIARY_SERVER_URL = getIntermediaryServerUrl();

console.log('Whop Intermediary Configuration:', {
  intermediaryUrl: INTERMEDIARY_SERVER_URL,
  envVar: process.env.VITE_WHOP_INTERMEDIARY_URL,
  nodeEnv: process.env.NODE_ENV,
  isDevelopment: process.env.NODE_ENV === 'development'
});

export function whopProxyMiddleware() {
  return {
    name: 'whop-proxy',
    configureServer(server) {
      server.middlewares.use('/api/whop-proxy', async (req, res, next) => {
        try {

          // Handle CORS preflight requests
          if (req.method === 'OPTIONS') {
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-whop-user-token');
            res.setHeader('Access-Control-Allow-Credentials', 'true');
            res.statusCode = 200;
            res.end();
            return;
          }

          // Parse query parameters
          const url = new URL(req.url, `http://${req.headers.host}`);
          const endpoint = url.searchParams.get('endpoint');
          
          if (!endpoint) {
            res.statusCode = 400;
            res.setHeader('Content-Type', 'application/json');
            res.end(JSON.stringify({
              success: false,
              error: 'Missing endpoint parameter'
            }));
            return;
          }

          // Construct the full URL to the intermediary server
          const targetUrl = `${INTERMEDIARY_SERVER_URL}/api/${endpoint}`;
          
          // Prepare headers to forward
          const forwardHeaders = {
            'Content-Type': 'application/json',
          };

          // Forward the Whop user token if present
          if (req.headers['x-whop-user-token']) {
            forwardHeaders['x-whop-user-token'] = req.headers['x-whop-user-token'];
          } else {

            // Check if this is a development environment and log helpful info
            if (process.env.NODE_ENV === 'development') {
            }
          }

          // Also forward other Whop-related headers that might be present
          const whopHeaders = [
            'x-whop-user-token',
            'x-whop-company-id',
            'x-whop-app-id',
            'x-whop-experience-id'
          ];

          whopHeaders.forEach(headerName => {
            if (req.headers[headerName]) {
              forwardHeaders[headerName] = req.headers[headerName];
            }
          });

          // Forward other relevant headers
          if (req.headers['authorization']) {
            forwardHeaders['authorization'] = req.headers['authorization'];
          }

          // Prepare the request options
          const requestOptions = {
            method: req.method,
            headers: forwardHeaders,
          };

          // Handle request body for POST/PUT requests
          if (req.method === 'POST' || req.method === 'PUT') {
            let body = '';
            req.on('data', chunk => {
              body += chunk.toString();
            });
            
            await new Promise(resolve => {
              req.on('end', resolve);
            });
            
            if (body) {
              requestOptions.body = body;
            }
          }

          console.log('Making request to intermediary:', {
            method: requestOptions.method,
            headers: Object.keys(requestOptions.headers),
            hasBody: !!requestOptions.body
          });

          // Make the request to the intermediary server
          const response = await fetch(targetUrl, requestOptions);
          const data = await response.text();

          console.log('Intermediary response:', {
            status: response.status,
            contentType: response.headers.get('content-type')
          });

          // Forward the response
          res.statusCode = response.status;
          res.setHeader('Content-Type', response.headers.get('content-type') || 'application/json');
          res.setHeader('Access-Control-Allow-Origin', '*');
          res.end(data);

        } catch (error) {
          console.error('Whop proxy error:', {
            error: error.message,
            stack: error.stack,
            url: req.url,
            method: req.method,
            targetUrl: targetUrl
          });
          res.statusCode = 500;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({
            success: false,
            error: 'Proxy error',
            message: error.message
          }));
        }
      });
    }
  };
}
