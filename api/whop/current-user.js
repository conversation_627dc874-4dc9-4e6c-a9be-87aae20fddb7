import { WhopServerSdk, makeUserTokenVerifier } from '@whop/api';

// Initialize Whop SDK (server-side only)
const whopSdk = WhopServerSdk({
  appId: process.env.VITE_WHOP_APP_ID,
  appApiKey: process.env.WHOP_API_KEY,
  onBehalfOfUserId: process.env.VITE_WHOP_AGENT_USER_ID,
  companyId: process.env.VITE_WHOP_COMPANY_ID,
});

// Create a custom user token verifier as fallback
const userTokenVerifier = makeUserTokenVerifier({
  appId: process.env.VITE_WHOP_APP_ID,
});

export default async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, x-whop-user-token');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {

    // Extract the user token from headers (as per Whop documentation)
    const userToken = req.headers['x-whop-user-token'];

    if (!userToken) {
      return res.status(400).json({
        success: false,
        error: 'No user token provided in headers',
        headers: Object.keys(req.headers),
        debug: 'Try /api/whop/debug-headers to see all headers'
      });
    }


    let userId;

    try {
      // Try the main SDK method first

      // Create a headers-like object that the SDK expects
      const headersObj = {
        get: (name) => req.headers[name.toLowerCase()],
        has: (name) => name.toLowerCase() in req.headers,
        forEach: (callback) => {
          Object.entries(req.headers).forEach(([key, value]) => {
            callback(value, key);
          });
        }
      };

      const result = await whopSdk.verifyUserToken(headersObj);
      userId = result.userId;
    } catch (sdkError) {

      try {
        // Fallback to custom verifier
        const result = await userTokenVerifier(userToken);
        userId = result?.userId; // Custom verifier returns userId field
      } catch (customError) {
          sdkError: sdkError.message,
          customError: customError.message
        });
        return res.status(401).json({
          success: false,
          error: 'Token verification failed',
          details: {
            sdkError: sdkError.message,
            customError: customError.message
          }
        });
      }
    }

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Invalid user token - no userId extracted'
      });
    }

    // Get user information from Whop API
    const user = await whopSdk.users.getUser({ userId });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }


    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        profilePicUrl: user.profilePicture?.url || user.profilePicUrl
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to verify user',
      message: error.message
    });
  }
};
